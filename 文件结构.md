# GoResoDownload 项目分析报告

## 📋 项目概述

**GoResoDownload** 是一个用Go语言开发的房地产媒体文件下载和管理系统，专门用于处理多个房地产委员会（Board）的媒体文件下载、更新和删除操作。

## 🏗️ 项目文件结构

```
goresodownload/
├── cmd/                          # 命令行工具和主程序
│   ├── goresodownload/          # 主程序入口
│   │   ├── main.go              # 主程序文件
│   │   ├── components.go        # 组件初始化
│   │   ├── processingQueue.go   # 队列处理逻辑
│   │   ├── system.go           # 系统相关功能
│   │   └── watch.go            # 监听逻辑
│   ├── batch/                   # 批处理工具
│   │   ├── addPhoP/            # 添加照片处理
│   │   └── addToQueue/         # 添加到队列
│   ├── l2test/                  # L2目录分布测试工具
│   └── utils/                   # 工具程序
├── docs/                        # 项目文档
│   ├── 20250515_架构设计.md      # 架构设计文档
│   ├── 20250515_api设计.md       # API设计文档
│   └── 其他设计文档...
├── test/                        # 测试文件
│   └── e2e/                    # 端到端测试
├── downloader.go               # 文件下载器
├── media_diff_analyzer.go      # 媒体差异分析器
├── priority_calculator.go      # 优先级计算器
├── resource_download_queue.go  # 资源下载队列
├── go.mod                      # Go模块定义
├── Makefile                    # 构建脚本
└── README.md                   # 项目说明
```

## 🎯 核心功能模块

### 1. **MediaDiffAnalyzer (媒体差异分析器)**
- 分析房产媒体字段的变更
- 对比新旧媒体列表，识别需要下载和删除的文件
- 支持多种媒体类型：图片、PDF、文档等

### 2. **Downloader (文件下载器)**
- 并发下载媒体文件（默认5个并发）
- 自动生成缩略图（240x160像素）
- 支持失败重试机制
- 文件完整性校验

### 3. **PriorityCalculator (优先级计算器)**
- 智能优先级算法，基于房产特征计算下载优先级
- **优先级因子：**
  - 无照片奖励：30,000分（最高优先级）
  - 活跃状态：10,000分
  - 当年房产：5,000分
  - GTA地区：500分
  - 新上市：300分（3天内）
  - 住宅类型：200分

### 4. **ResourceDownloadQueue (资源下载队列)**
- 基于优先级的队列管理
- 批量处理（默认批次大小：4）
- 锁定机制防止重复处理
- 自动清理已完成任务

### 5. **目录管理系统**
- **L1目录**：基于年-周的时间结构（如750、751等）
- **L2目录**：基于UUID5的分散目录结构
- **文件命名**：`L1/L2/sid_base62.jpg`

## 🏢 支持的Board类型

| Board | 全称 | L2目录数量 | 监听表 |
|-------|------|-----------|--------|
| TRB | Toronto Regional Board | 512 | reso_treb_evow_merged |
| DDF | Data Distribution Facility | 1024 | reso_crea_raw |
| BRE | BC Real Estate | 64 | bridge_bcre_raw |
| CLG | Calgary | 64 | - |
| OTW | Ottawa | 64 | - |
| EDM | Edmonton | 64 | reso_edm_raw |
| CAR | Canadian Real Estate | 256 | mls_car_raw_records |
| USER | User defined | 512 | - |

## 🔄 核心工作流程

```mermaid
graph TD
    A[MongoDB Change Stream] --> B[MediaDiffAnalyzer]
    B --> C[PriorityCalculator]
    C --> D[ResourceDownloadQueue]
    D --> E[Downloader]
    E --> F[文件存储]
    F --> G[更新Merged表]

    H[失败任务] --> I[重试机制]
    I --> D
```

1. **变更监听**：MongoDB Change Streams监听各Board媒体字段变更
2. **差异分析**：分析新旧媒体列表差异，生成下载/删除任务
3. **优先级计算**：根据房产特征计算任务优先级
4. **队列管理**：将任务加入优先级队列
5. **并发下载**：执行文件下载和删除操作
6. **状态同步**：更新merged表的phoHL、tnHL、docHL字段

## 🛠️ 技术栈

### 核心依赖
- **Go 1.24.4**
- **MongoDB** - 数据存储和变更监听
- **real-rm内部包**：
  - `gobase` - 基础功能
  - `gomongo` - MongoDB操作
  - `golevelstore` - 层级存储
  - `golog` - 日志系统
  - `gowatch` - 变更监听

### 特性
- 支持干运行模式（-dryrun）
- 进程监控和状态跟踪
- 内存使用监控
- HTTP连接池管理
- 并发安全设计

## 🚀 使用方式

### 构建项目
```bash
cd goresodownload
make build  # 构建主程序到 bin/goresodownload.bin
```

### 运行示例
```bash
# 干运行模式
./start.sh -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force -dryrun"

# 生产模式
./start.sh -t batch -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force"

# 自定义批次大小
./start.sh -t batch -n goresodownloadCAR -d "goresodownload" -cmd "bin/goresodownload.bin -board CAR -force -batchSize 3"
```

## 📊 数据流向

### 输入数据
- 各Board的原始数据表（如mls_car_raw_records）
- Media字段变更事件

### 输出数据
- 本地媒体文件存储
- merged表更新：
  - `phoHL`: 图片hash列表
  - `tnHL`: 缩略图hash
  - `docHL`: 文档hash列表

## 🎯 项目优势

1. **高性能**：并发下载和处理，支持大规模数据
2. **智能调度**：基于房产特征的优先级算法
3. **可靠性**：失败重试、进程监控、状态跟踪
4. **可扩展**：模块化设计，易于添加新Board类型
5. **可维护**：详细的文档和测试覆盖

这个项目是一个成熟的企业级媒体文件管理系统，专门针对房地产行业的需求进行了优化设计。


## 需求:

1. 增加maxPicSz,avgPicZz,totPicSz,phodlNum,phoDl(ts)          goresodownload
2. add stat for file write and download     Mbs(平均下载速度/平均写入速度)     总平均图片大小                   gofile
3. speed 做一下排序： needAnalyze(863):54.78/m addedToQueue(862):54.72/m prop(91):5.776/m downloadMedia(1190):75.54/m deleteMedia(429):27.23/m downloadError(2):0.127/m   gospeedmeter
4. const magic number改为常量
5. query index使用确认， expalin
6. 图片下载为Large，显示很慢，下载图片改尺寸， 现有图片的尺寸修改
7. 300k以上图片需要压缩到300k以下, 1024*1024


下载图片又删掉，watch没有判断新字段的unset
状态更新后，房源的media字段会被unset掉,但是phoUrls还在


go mod tidy

1. 增加maxPicSz,avgPicZz,totPicSz,phodlNum,phoDl(ts) + 图片大 显示慢 下载时候，直接改尺寸【300k以下，10241024或者14801480】 已经下载的也需要改，等都运行完
2. add stat for file write and download Mbs 总平均图片大小
3. speed 做一下排序： needAnalyze(863):54.78/m addedToQueue(862):54.72/m prop(91):5.776/m downloadMedia(1190):75.54/m deleteMedia(429):27.23/m downloadError(2):0.127/m
4. const
5. queue index 确认 explain
6. 水印添加在显示图片@liurui
7. 支持图片更新，操作就是删除phoLH字段，并高优先级添加到queue中