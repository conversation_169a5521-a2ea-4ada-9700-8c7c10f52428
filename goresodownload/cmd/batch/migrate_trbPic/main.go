/*
###
Description:    Batch migrate downloaded property images to new directory structure

Usage:         ./start.sh -n migrate_trbPic -d "goresodownload" -cmd "cmd/batch/migrate_trbPic/main.go -board=TRB -dir=/1200 -dryrun"

Create date:    2025-07-28
Author:         Assistant
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for file path generation")
	dirFlag    = flag.String("dir", "", "Target directory to migrate (e.g., /1200/abc12)")
	speedMeter *gospeedmeter.SpeedMeter
	dirStore   *levelStore.DirKeyStore
	startTime  = time.Now()
)

// MigrationStats holds statistics for the migration process
type MigrationStats struct {
	TotalProcessed   int64
	NeedsMigration   int64
	AlreadyCorrect   int64
	MigrationSuccess int64
	MigrationFailed  int64
	DatabaseUpdated  int64
	DatabaseFailed   int64
	FilesNotFound    int64
}

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Initialize dirStore for directory statistics
	collection := gomongo.Coll("rni", "file_server")
	var err error
	dirStore, err = levelStore.NewDirKeyStore("", collection, "")
	if err != nil {
		golog.Fatalf("Failed to create dirKeyStore: %v", err)
	}
	if dirStore == nil {
		golog.Fatalf("dirKeyStore is nil")
	}
}

func main() {
	// Create context with timeout to prevent hanging
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Minute)
	defer cancel()

	golog.Info("Starting migrate_trbPic batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("migrate_trbPic batch process completed successfully")
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()

	// Validate required parameters
	if *dirFlag == "" {
		return fmt.Errorf("dir parameter is required (e.g., -dir=/1200/abc12)")
	}

	// Normalize directory path (ensure it starts with /)
	if !strings.HasPrefix(*dirFlag, "/") {
		*dirFlag = "/" + *dirFlag
	}

	// Determine query type based on directory format
	var queryType string
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		queryType = "prefix match (regex)"
	} else {
		queryType = "exact match"
	}

	golog.Info("Starting migrate_trbPic batch processing",
		"dryrun", *dryrunFlag,
		"board", *boardFlag,
		"dir", *dirFlag,
		"queryType", queryType)

	// Validate board flag
	collectionName, exists := goresodownload.BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Get storage paths for file operations
	storagePaths, err := levelStore.GetImageDir(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get image directories: %w", err)
	}
	if len(storagePaths) == 0 {
		return fmt.Errorf("no image directories configured for board %s", *boardFlag)
	}

	// Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	golog.Info("Processing collection",
		"board", *boardFlag,
		"collection", collectionName,
		"dryrun", *dryrunFlag,
		"storagePaths", storagePaths)

	// Query documents with the specified phoP directory
	// Support both exact match and prefix match (regex)
	var query bson.M
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		// If directory ends with "/" or only has one "/" (L1 level), use regex for prefix match
		// Example: "/1200" should match "/1200/abc12", "/1200/def34", etc.
		regexPattern := "^" + strings.TrimSuffix(*dirFlag, "/") + "/"
		query = bson.M{
			"phoP": bson.M{"$regex": regexPattern},
		}
		golog.Info("Using regex pattern for directory matching", "pattern", regexPattern)
	} else {
		// Exact match for full paths like "/1200/abc12"
		query = bson.M{
			"phoP": *dirFlag,
		}
		golog.Info("Using exact match for directory", "dir", *dirFlag)
	}

	// 根据board类型确定需要查询的字段
	projection := bson.D{
		{Key: "_id", Value: 1},
		{Key: "ts", Value: 1},
		{Key: "ListingKey", Value: 1},
		{Key: "ListingId", Value: 1},
		{Key: "phoP", Value: 1},
		{Key: "phoLH", Value: 1},
		{Key: "tnLH", Value: 1},
		{Key: "ListingContractDate", Value: 1},
		{Key: "ModificationTimestamp", Value: 1},
		{Key: "OriginalEntryTimestamp", Value: 1},
		{Key: "BridgeModificationTimestamp", Value: 1},
	}

	options := gomongo.QueryOptions{
		Projection: projection,
	}

	// Get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query, options)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	// Initialize migration stats
	stats := &MigrationStats{}

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, coll, item, storagePaths, stats)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			printFinalStats(stats, duration)
			if err != nil {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to migrate images if needed
func processItem(ctx context.Context, coll *gomongo.MongoCollection, item interface{}, storagePaths []string, stats *MigrationStats) error {
	golog.Debug("Processing item started")

	// Track processing speed
	speedMeter.Check("processed", 1)
	stats.TotalProcessed++

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully")

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		golog.Error("Failed to extract _id", "doc", doc)
		speedMeter.Check("idErrors", 1)
		return fmt.Errorf("failed to extract _id")
	}

	// 根据board类型确定使用哪个字段作为listingKey
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get listing key", "error", err, "_id", id)
		speedMeter.Check("listingKeyErrors", 1)
		return err
	}

	currentPhoP, ok := doc["phoP"].(string)
	if !ok {
		golog.Error("Failed to extract phoP", "doc", doc, "_id", id)
		speedMeter.Check("phoPErrors", 1)
		return fmt.Errorf("failed to extract phoP")
	}

	// 使用新的PropTs获取逻辑
	ts, err := goresodownload.GetPropTsForPath(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get PropTs for path",
			"_id", id, "board", *boardFlag, "error", err)
		speedMeter.Check("tsErrors", 1)
		return fmt.Errorf("failed to get PropTs for path: %w", err)
	}

	// Get new file path using levelStore
	newPhoP, err := levelStore.GetFullFilePathForProp(ts, *boardFlag, listingKey)
	if err != nil {
		golog.Error("Failed to get full file path",
			"_id", id,
			"listingKey", listingKey,
			"ts", ts,
			"error", err)
		speedMeter.Check("filePathErrors", 1)
		return fmt.Errorf("failed to get full file path: %w", err)
	}

	// Check if migration is needed
	if currentPhoP == newPhoP {
		golog.Debug("Path already correct, no migration needed",
			"_id", id,
			"path", currentPhoP)
		stats.AlreadyCorrect++
		speedMeter.Check("alreadyCorrect", 1)
		return nil
	}

	golog.Info("Migration needed",
		"_id", id,
		"listingKey", listingKey,
		"currentPath", currentPhoP,
		"newPath", newPhoP)
	stats.NeedsMigration++

	// Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run mode: Would migrate images",
			"_id", id,
			"listingKey", listingKey,
			"from", currentPhoP,
			"to", newPhoP)
		return nil
	}

	// Perform actual migration
	renameResults, err := migrateImages(doc, currentPhoP, newPhoP, storagePaths)
	if err != nil {
		golog.Error("Failed to migrate images",
			"_id", id,
			"from", currentPhoP,
			"to", newPhoP,
			"error", err)
		stats.MigrationFailed++
		speedMeter.Check("migrationFailed", 1)
		// 图片迁移失败时不更新数据库，直接返回错误
		return fmt.Errorf("image migration failed, database not updated: %w", err)
	}

	// 图片迁移成功，记录成功统计
	stats.MigrationSuccess++
	speedMeter.Check("migrationSuccess", 1)

	// 只有在图片迁移成功时才更新数据库记录
	update := bson.M{
		"$set": bson.M{
			"phoP": newPhoP,
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		golog.Error("Failed to update document, rolling back image migration",
			"_id", id,
			"newPath", newPhoP,
			"error", err)

		// 数据库更新失败时，回滚图片迁移
		rollbackImageMigration(renameResults)

		stats.DatabaseFailed++
		speedMeter.Check("updateErrors", 1)
		return fmt.Errorf("failed to update document, image migration rolled back: %w", err)
	}

	// Track successful database updates
	stats.DatabaseUpdated++
	speedMeter.Check("updated", 1)

	golog.Info("Successfully processed document",
		"_id", id,
		"listingKey", listingKey,
		"from", currentPhoP,
		"to", newPhoP,
		"migrationSuccess", true,
		"modifiedCount", result.ModifiedCount)

	return nil
}

// RenameResult tracks the result of migrating a single image file using hard link strategy
type RenameResult struct {
	OldPath string
	NewPath string
	Success bool
}

// migrateImages migrates image files from old directory to new directory using hard link strategy
// Creates hard links to new location, then removes source files
// Returns a list of successfully migrated files for potential rollback and any error encountered
func migrateImages(doc bson.M, oldPath, newPath string, storagePaths []string) ([]RenameResult, error) {
	// Extract phoLH (photo list hash) to get the list of image files
	phoLH, ok := doc["phoLH"]
	if !ok || phoLH == nil {
		golog.Debug("No phoLH found, skipping file migration", "oldPath", oldPath, "newPath", newPath)
		return nil, nil // No images to migrate
	}

	// Convert phoLH array to slice of base62 strings
	var imageHashes []string
	if phoLHArray, ok := phoLH.([]interface{}); ok {
		for _, hash := range phoLHArray {
			// Convert each hash (assumed to be int32) to base62 string
			if hashInt32, ok := hash.(int32); ok {
				base62Hash, err := levelStore.Int32ToBase62(hashInt32)
				if err != nil {
					golog.Error("Failed to convert hash to base62", "hash", hashInt32, "error", err)
					continue
				}
				imageHashes = append(imageHashes, base62Hash)
			} else {
				golog.Warn("Expected int32 hash in phoLH", "type", fmt.Sprintf("%T", hash), "value", hash)
				continue
			}
		}
	} else {
		return nil, fmt.Errorf("phoLH is not an array, got type: %T", phoLH)
	}

	// Handle tnLH (thumbnail hash) - assumed to be int32
	tnLH, ok := doc["tnLH"]
	if ok && tnLH != nil {
		if tnHashInt32, ok := tnLH.(int32); ok {
			base62Hash, err := levelStore.Int32ToBase62(tnHashInt32)
			if err != nil {
				golog.Error("Failed to convert tnLH to base62", "hash", tnHashInt32, "error", err)
			} else {
				imageHashes = append(imageHashes, base62Hash)
			}
		} else {
			golog.Warn("Expected int32 for tnLH", "type", fmt.Sprintf("%T", tnLH), "value", tnLH)
		}
	}

	if len(imageHashes) == 0 {
		golog.Debug("No image hashes found, skipping file migration", "oldPath", oldPath, "newPath", newPath)
		return nil, nil
	}

	// 根据board类型确定使用哪个字段作为listingKey
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return nil, fmt.Errorf("failed to get listing key for file migration: %w", err)
	}

	golog.Info("Starting image migration with hard link strategy",
		"oldPath", oldPath,
		"newPath", newPath,
		"imageCount", len(imageHashes),
		"listingKey", listingKey)

	// Track rename results for potential rollback
	var allRenameResults []RenameResult
	var migrationErrors []string
	migratedCount := 0

	// Phase 1: Rename all images
	for _, storagePath := range storagePaths {
		oldDir := filepath.Join(storagePath, strings.TrimPrefix(oldPath, "/"))
		newDir := filepath.Join(storagePath, strings.TrimPrefix(newPath, "/"))

		// Create new directory if it doesn't exist
		if err := os.MkdirAll(newDir, 0755); err != nil {
			migrationErrors = append(migrationErrors, fmt.Sprintf("failed to create directory %s: %v", newDir, err))
			break // Stop processing if directory creation fails
		}

		// Rename each image file
		for _, hash := range imageHashes {
			// Generate filename: ListingKey_hash.jpg
			filename := fmt.Sprintf("%s_%s.jpg", listingKey, hash)
			oldFilePath := filepath.Join(oldDir, filename)
			newFilePath := filepath.Join(newDir, filename)

			renameResult := RenameResult{
				OldPath: oldFilePath,
				NewPath: newFilePath,
				Success: false,
			}

			// Check if source file exists
			if _, err := os.Stat(oldFilePath); os.IsNotExist(err) {
				golog.Error("Source file not found - database inconsistency detected",
					"file", oldFilePath,
					"listingKey", listingKey,
					"hash", hash)
				migrationErrors = append(migrationErrors, fmt.Sprintf("source file not found: %s", oldFilePath))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on file not found error
			}

			// Check if destination file already exists
			if _, err := os.Stat(newFilePath); err == nil {
				// If destination exists, check if source still exists
				if _, err := os.Stat(oldFilePath); err == nil {
					// Both exist, remove source file to complete the migration
					if err := os.Remove(oldFilePath); err != nil {
						golog.Warn("Failed to remove source file when destination already exists",
							"source", oldFilePath, "destination", newFilePath, "error", err)
					}
				}
				golog.Debug("Destination file already exists, treating as successful", "file", newFilePath)
				renameResult.Success = true
				allRenameResults = append(allRenameResults, renameResult)
				migratedCount++
				continue
			}

			// Perform hard link operation followed by source file removal
			// Step 1: Create hard link
			if err := os.Link(oldFilePath, newFilePath); err != nil {
				migrationErrors = append(migrationErrors, fmt.Sprintf("failed to create hard link from %s to %s: %v", oldFilePath, newFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			// Step 2: Remove source file after successful hard link creation
			if err := os.Remove(oldFilePath); err != nil {
				// Hard link was created but source removal failed - need to clean up the hard link
				if removeErr := os.Remove(newFilePath); removeErr != nil {
					golog.Error("Failed to cleanup hard link after source removal failure",
						"hardLink", newFilePath, "cleanupError", removeErr)
				}
				migrationErrors = append(migrationErrors, fmt.Sprintf("failed to remove source file %s after hard link creation: %v", oldFilePath, err))
				allRenameResults = append(allRenameResults, renameResult) // Add failed result for tracking
				break                                                     // Stop processing current storage path on first failure
			}

			renameResult.Success = true
			allRenameResults = append(allRenameResults, renameResult)
			migratedCount++
			golog.Debug("Successfully migrated file using hard link", "from", oldFilePath, "to", newFilePath)
		}

		// If there were errors in current storage path, stop processing other storage paths
		if len(migrationErrors) > 0 {
			break
		}
	}

	// If migration phase failed, rollback successfully migrated files and return error
	if len(migrationErrors) > 0 {
		golog.Warn("Hard link migration phase failed, rolling back successfully migrated files",
			"errorCount", len(migrationErrors),
			"migrationResultsCount", len(allRenameResults))

		// Rollback successfully migrated files
		rollbackImageMigration(allRenameResults)
		return nil, fmt.Errorf("hard link migration errors: %s", strings.Join(migrationErrors, "; "))
	}

	golog.Info("Image migration completed successfully",
		"oldPath", oldPath,
		"newPath", newPath,
		"totalImages", len(imageHashes),
		"migratedCount", migratedCount,
		"strategy", "hard link")

	// Update dirStore statistics after successful migration
	if !*dryrunFlag {
		if err := updateDirStoreStats(oldPath, newPath, migratedCount); err != nil {
			golog.Warn("Failed to update dirStore stats", "error", err)
			// Don't return error as migration was successful
		}
	}

	return allRenameResults, nil
}

// getListingKey extracts the listing key from the document based on the board type.
func getListingKey(doc bson.M, board string) (string, error) {
	var listingKey string
	var ok bool

	id, _ := doc["_id"].(string) // For logging purposes

	if board == "TRB" {
		listingKey, ok = doc["ListingKey"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingKey for TRB board, _id: %s", id)
		}
	} else {
		listingKey, ok = doc["ListingId"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingId for board %s, _id: %s", board, id)
		}
	}
	return listingKey, nil
}

// rollbackImageMigration rolls back successfully migrated files to their original locations
// Uses hard link + remove strategy to maintain consistency with forward migration
func rollbackImageMigration(renameResults []RenameResult) {
	if len(renameResults) == 0 {
		return
	}

	golog.Info("Starting rollback of image migration", "fileCount", len(renameResults))
	rollbackCount := 0
	rollbackErrors := 0

	for _, result := range renameResults {
		if !result.Success {
			continue // Skip failed migrations
		}

		// Attempt to rollback using hard link + remove strategy
		// Step 1: Create hard link from new location back to old location
		if err := os.Link(result.NewPath, result.OldPath); err != nil {
			golog.Error("Failed to create hard link during rollback",
				"from", result.NewPath,
				"to", result.OldPath,
				"error", err)
			rollbackErrors++
			continue
		}

		// Step 2: Remove file from new location after successful hard link creation
		if err := os.Remove(result.NewPath); err != nil {
			// Hard link was created but removal failed - try to clean up the hard link
			if removeErr := os.Remove(result.OldPath); removeErr != nil {
				golog.Error("Failed to cleanup hard link after removal failure during rollback",
					"hardLink", result.OldPath, "cleanupError", removeErr)
			}
			golog.Error("Failed to remove file from new location during rollback",
				"file", result.NewPath,
				"error", err)
			rollbackErrors++
		} else {
			golog.Debug("Successfully rolled back file using hard link",
				"from", result.NewPath,
				"to", result.OldPath)
			rollbackCount++
		}
	}

	golog.Info("Rollback completed",
		"totalFiles", len(renameResults),
		"rollbackSuccess", rollbackCount,
		"rollbackErrors", rollbackErrors)
}

// printFinalStats prints the final migration statistics
func printFinalStats(stats *MigrationStats, duration time.Duration) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("MIGRATION SUMMARY")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("Total Processing Time: %v\n", duration)
	fmt.Printf("Total Records Processed: %d\n", stats.TotalProcessed)
	fmt.Printf("Records Already Correct: %d\n", stats.AlreadyCorrect)
	fmt.Printf("Records Needing Migration: %d\n", stats.NeedsMigration)

	if !*dryrunFlag {
		fmt.Printf("File Migration Success: %d\n", stats.MigrationSuccess)
		fmt.Printf("File Migration Failed: %d\n", stats.MigrationFailed)
		fmt.Printf("Database Updates Success: %d\n", stats.DatabaseUpdated)
		fmt.Printf("Database Updates Failed: %d\n", stats.DatabaseFailed)
		fmt.Printf("Files Not Found: %d\n", stats.FilesNotFound)
	} else {
		fmt.Println("Mode: DRY RUN (no actual changes made)")
	}

	fmt.Println(strings.Repeat("=", 60))

	// Log the same information
	golog.Info("Migration completed",
		"duration", duration,
		"totalProcessed", stats.TotalProcessed,
		"alreadyCorrect", stats.AlreadyCorrect,
		"needsMigration", stats.NeedsMigration,
		"migrationSuccess", stats.MigrationSuccess,
		"migrationFailed", stats.MigrationFailed,
		"databaseUpdated", stats.DatabaseUpdated,
		"databaseFailed", stats.DatabaseFailed,
		"filesNotFound", stats.FilesNotFound,
		"dryrun", *dryrunFlag)
}

// updateDirStoreStats updates directory statistics after file migration
// oldPath: source directory path (e.g., "/1200/abc12")
// newPath: destination directory path (e.g., "/1274/ffc41")
// fileCount: number of files migrated
func updateDirStoreStats(oldPath, newPath string, fileCount int) error {
	if fileCount <= 0 {
		return nil // No files migrated, no stats to update
	}

	// Parse L1 and L2 from old path
	oldL1, oldL2, err := parseL1L2FromPath(oldPath)
	if err != nil {
		return fmt.Errorf("failed to parse old path %s: %w", oldPath, err)
	}

	// Parse L1 and L2 from new path
	newL1, newL2, err := parseL1L2FromPath(newPath)
	if err != nil {
		return fmt.Errorf("failed to parse new path %s: %w", newPath, err)
	}

	// Update statistics: decrease old directory, increase new directory
	// Entity count is 0 because we're moving files, not adding/removing properties
	dirStore.AddDirStats(oldL1, oldL2, 0, -fileCount) // Decrease file count in old directory
	dirStore.AddDirStats(newL1, newL2, 0, fileCount)  // Increase file count in new directory

	golog.Info("Updated dirStore statistics",
		"oldPath", oldPath,
		"newPath", newPath,
		"oldL1", oldL1,
		"oldL2", oldL2,
		"newL1", newL1,
		"newL2", newL2,
		"fileCount", fileCount)

	return nil
}

// parseL1L2FromPath parses L1 and L2 from a path like "/1200/abc12"
func parseL1L2FromPath(path string) (string, string, error) {
	// Remove leading slash and split by "/"
	path = strings.TrimPrefix(path, "/")
	parts := strings.Split(path, "/")

	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid path format, expected /L1/L2, got: %s", path)
	}

	l1 := parts[0]
	l2 := parts[1]

	if l1 == "" || l2 == "" {
		return "", "", fmt.Errorf("empty L1 or L2 in path: %s", path)
	}

	return l1, l2, nil
}
